<script lang="ts">
  import { Briefcase, FileText, Eye, TrendingUp, ArrowUpRight, Clock, MapPin, DollarSign } from 'lucide-svelte'
  import type { PageData } from './$types'
  
  let { data }: { data: PageData } = $props()

  let { candidateProfile } = $derived(data)
  
  // Mock data for dashboard stats with better formatting
  const stats = [
    { label: 'Applications', value: '12', icon: FileText, color: 'blue', change: '+3 this week' },
    { label: 'Profile Views', value: '48', icon: Eye, color: 'green', change: '+12 today' },
    { label: 'Job Matches', value: '6', icon: Briefcase, color: 'purple', change: '2 new' },
    { label: 'Response Rate', value: '25%', icon: TrendingUp, color: 'orange', change: '+5% this month' }
  ]
  
  const recentApplications = [
    { 
      job: 'Senior Software Engineer', 
      company: 'TechCorp', 
      location: 'San Francisco, CA',
      salary: '$120k - $160k',
      status: 'Under Review', 
      date: '2 days ago',
      urgent: false
    },
    { 
      job: 'Full Stack Developer', 
      company: 'StartupXYZ', 
      location: 'Remote',
      salary: '$90k - $120k',
      status: 'Interview Scheduled', 
      date: '1 week ago',
      urgent: true
    },
    { 
      job: 'Frontend Developer', 
      company: 'BigTech Inc', 
      location: 'Austin, TX',
      salary: '$85k - $110k',
      status: 'Applied', 
      date: '1 week ago',
      urgent: false
    }
  ]
  
  const recommendedJobs = [
    { 
      title: 'React Developer', 
      company: 'InnovateLabs', 
      location: 'Remote', 
      salary: '$80k - $100k',
      type: 'Full-time',
      posted: '2 days ago'
    },
    { 
      title: 'Software Engineer', 
      company: 'TechFlow', 
      location: 'San Francisco, CA', 
      salary: '$90k - $120k',
      type: 'Full-time',
      posted: '3 days ago'
    },
    { 
      title: 'Full Stack Developer', 
      company: 'CloudServe', 
      location: 'Austin, TX', 
      salary: '$85k - $110k',
      type: 'Contract',
      posted: '1 week ago'
    }
  ]

  function getStatusColor(status: string): string {
    switch (status) {
      case 'Interview Scheduled':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      case 'Under Review':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'Applied':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }
</script>

<svelte:head>
  <title>Dashboard - Candidate Portal</title>
</svelte:head>

<div class="space-y-6">
  <!-- Welcome Section -->
  <div class="bg-gradient-to-br from-blue-600 to-blue-700 dark:from-blue-700 dark:to-blue-800 rounded-xl p-6 text-white shadow-lg">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-xl sm:text-2xl font-bold mb-2">
          Welcome back, {candidateProfile?.first_name || 'there'}!
        </h1>
        <p class="text-blue-100 text-sm sm:text-base">
          Ready to find your next opportunity? Let's get started.
        </p>
      </div>
      <div class="mt-4 sm:mt-0">
        <button class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
          Update Profile
        </button>
      </div>
    </div>
  </div>

  <!-- Optional Profile Completion Notice -->
  {#if candidateProfile && !candidateProfile.phone}
    <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl p-4">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-amber-100 dark:bg-amber-900/50 rounded-lg flex items-center justify-center">
            <Briefcase class="w-5 h-5 text-amber-600 dark:text-amber-400" />
          </div>
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-amber-800 dark:text-amber-200">Complete your profile when ready</h3>
          <p class="mt-1 text-sm text-amber-700 dark:text-amber-300">
            Adding additional details like your phone number can help employers reach you faster.
          </p>
          <div class="mt-3">
            <a
              href="/profile/settings"
              class="text-sm font-medium text-amber-800 dark:text-amber-200 hover:text-amber-900 dark:hover:text-amber-100 underline"
            >
              Complete profile →
            </a>
          </div>
        </div>
        <button
          onclick={(e) => {
            const notice = e.currentTarget.closest('.bg-amber-50') as HTMLElement;
            if (notice) notice.style.display = 'none';
          }}
          class="flex-shrink-0 ml-4 text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-200"
          title="Dismiss"
        >
          <span class="sr-only">Dismiss</span>
          ×
        </button>
      </div>
    </div>
  {/if}

  <!-- Stats Grid -->
  <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
    {#each stats as stat}
      {@const IconComponent = stat.icon}
      <div class="bg-white dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-800 p-4 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between mb-3">
          <div class="w-8 h-8 bg-{stat.color}-100 dark:bg-{stat.color}-900/30 rounded-lg flex items-center justify-center">
            <IconComponent class="w-4 h-4 text-{stat.color}-600 dark:text-{stat.color}-400" />
          </div>
          <ArrowUpRight class="w-4 h-4 text-gray-400" />
        </div>
        <div>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-300">{stat.label}</p>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{stat.change}</p>
        </div>
      </div>
    {/each}
  </div>

  <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
    <!-- Recent Applications -->
    <div class="bg-white dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-800 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Applications</h2>
          <a href="/applications" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium">
            View all
          </a>
        </div>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          {#each recentApplications as app}
            <div class="flex items-start gap-4 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
              <div class="flex-1 min-w-0">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h3 class="font-medium text-gray-900 dark:text-white text-sm">{app.job}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">{app.company}</p>
                    <div class="flex items-center gap-2 mt-1 text-xs text-gray-500 dark:text-gray-400">
                      <MapPin class="w-3 h-3" />
                      <span>{app.location}</span>
                      <span>•</span>
                      <DollarSign class="w-3 h-3" />
                      <span>{app.salary}</span>
                    </div>
                  </div>
                  <div class="text-right ml-4">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {getStatusColor(app.status)}">
                      {app.status}
                    </span>
                    <div class="flex items-center gap-1 mt-1 text-xs text-gray-500 dark:text-gray-400">
                      <Clock class="w-3 h-3" />
                      <span>{app.date}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    </div>

    <!-- Recommended Jobs -->
    <div class="bg-white dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-800 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-800">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Recommended for You</h2>
          <a href="/jobs" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium">
            Browse all
          </a>
        </div>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          {#each recommendedJobs as job}
            <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-sm cursor-pointer transition-all">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900 dark:text-white text-sm">{job.title}</h3>
                  <p class="text-sm text-gray-600 dark:text-gray-300">{job.company}</p>
                  <div class="flex items-center gap-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <div class="flex items-center gap-1">
                      <MapPin class="w-3 h-3" />
                      <span>{job.location}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <DollarSign class="w-3 h-3" />
                      <span>{job.salary}</span>
                    </div>
                    <span class="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-2 py-0.5 rounded-full text-xs">
                      {job.type}
                    </span>
                  </div>
                </div>
                <div class="text-right ml-4">
                  <div class="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                    <Clock class="w-3 h-3" />
                    <span>{job.posted}</span>
                  </div>
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-white dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-800 p-6">
    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h2>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      <a href="/profile" class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-sm transition-all group">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors">
            <Briefcase class="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-900 dark:text-white">Update Profile</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">Keep your information current</p>
        </div>
      </a>
      
      <a href="/jobs" class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-sm transition-all group">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center group-hover:bg-green-200 dark:group-hover:bg-green-900/50 transition-colors">
            <FileText class="w-5 h-5 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-900 dark:text-white">Search Jobs</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">Find new opportunities</p>
        </div>
      </a>
      
      <a href="/settings" class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-sm transition-all group">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center group-hover:bg-purple-200 dark:group-hover:bg-purple-900/50 transition-colors">
            <TrendingUp class="w-5 h-5 text-purple-600 dark:text-purple-400" />
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-900 dark:text-white">Job Preferences</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">Customize your search</p>
        </div>
      </a>
    </div>
  </div>
</div>
