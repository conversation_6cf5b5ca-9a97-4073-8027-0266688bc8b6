import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root .env file
dotenv.config({ path: path.resolve(process.cwd(), '../../.env') });

export default defineConfig({
	plugins: [sveltekit()],
	server: {
		port: 3004,
		host: true,
		strictPort: true // Fail if port is not available
	}
});
