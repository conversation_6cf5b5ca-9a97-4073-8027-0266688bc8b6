import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import type { BusinessLeadEnhanced, RegistrationAnalytics } from '@psii/shared-types';

export const load: PageServerLoad = async ({ locals: { supabase }, depends }) => {
	// Add dependency for manual refresh
	depends('app:pending-registrations');

	try {
		// Fetch all business leads with enhanced fields
		const { data: leads, error: leadsError } = await supabase
			.from('business_leads')
			.select(`
				*,
				domain_extracted,
				domain_detection_result,
				activation_admin_email,
				activation_admin_name,
				company_admin_notified_at,
				priority_level
			`)
			.order('created_at', { ascending: false });

		if (leadsError) {
			console.error('Error fetching business leads:', leadsError);
			throw error(500, 'Failed to fetch pending applications.');
		}

		// Calculate analytics
		const analytics = calculateAnalytics(leads as BusinessLeadEnhanced[]);

		return { 
			leads: leads as BusinessLeadEnhanced[], 
			analytics 
		};

	} catch (e) {
		console.error('Error in pending applications load function:', e);
		throw error(500, 'Failed to load pending applications.');
	}
};

function calculateAnalytics(leads: BusinessLeadEnhanced[]): RegistrationAnalytics {
	const total_registrations = leads.length;
	const pending_count = leads.filter(l => l.status === 'pending_review').length;
	const approved_count = leads.filter(l => l.status === 'approved').length;
	const rejected_count = leads.filter(l => l.status === 'rejected').length;
	
	const new_company_count = leads.filter(l => 
		l.domain_detection_result?.type === 'NEW_COMPANY'
	).length;
	
	const existing_domain_count = leads.filter(l => 
		l.domain_detection_result?.type === 'EXISTING_CUSTOMER'
	).length;

	// Calculate average approval time for approved applications
	const approved_leads = leads.filter(l => l.status === 'approved');
	let avg_approval_time_hours = 0;
	
	if (approved_leads.length > 0) {
		const total_hours = approved_leads.reduce((sum, lead) => {
			const created = new Date(lead.created_at).getTime();
			const updated = new Date(lead.updated_at).getTime();
			return sum + ((updated - created) / (1000 * 60 * 60)); // Convert to hours
		}, 0);
		
		avg_approval_time_hours = Math.round(total_hours / approved_leads.length);
	}

	// Domain breakdown for analytics
	const domain_map = new Map<string, { count: number; last_registration: string }>();
	
	leads.forEach(lead => {
		if (lead.domain_extracted) {
			const existing = domain_map.get(lead.domain_extracted);
			if (existing) {
				existing.count++;
				if (new Date(lead.created_at) > new Date(existing.last_registration)) {
					existing.last_registration = lead.created_at;
				}
			} else {
				domain_map.set(lead.domain_extracted, {
					count: 1,
					last_registration: lead.created_at
				});
			}
		}
	});

	const domains_breakdown = Array.from(domain_map.entries())
		.map(([domain, data]) => ({ domain, ...data }))
		.sort((a, b) => b.count - a.count)
		.slice(0, 10); // Top 10 domains

	return {
		total_registrations,
		pending_count,
		approved_count,
		rejected_count,
		new_company_count,
		existing_domain_count,
		avg_approval_time_hours,
		domains_breakdown
	};
}
