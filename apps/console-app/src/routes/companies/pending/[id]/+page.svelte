<script lang="ts">
	import type { PageData, ActionData } from './$types'
	import { Building2, User, Mail, Phone, Clock, Hash, Briefcase, DollarSign, Globe, CheckCircle, XCircle, Edit, AlertTriangle } from 'lucide-svelte'
	import { enhance } from '$app/forms'
	import type { BusinessLeadEnhanced, AdminEmailSelection } from '@psii/shared-types'
	import DomainDetectionBadge from '$lib/components/registration/DomainDetectionBadge.svelte'
	import AdminEmailSelector from '$lib/components/registration/AdminEmailSelector.svelte'

	export let data: PageData
	export let form: ActionData

	$: lead = data.lead as BusinessLeadEnhanced
	$: existing_admins = data.existing_admins || []
	
	let loading = false
	let showEmailCustomization = false
	let selected_admin: AdminEmailSelection | null = null

	// Initialize default admin selection
	$: if (lead && !selected_admin) {
		selected_admin = {
			email: lead.activation_admin_email || lead.primary_contact.email,
			name: lead.activation_admin_name || `${lead.primary_contact.first_name} ${lead.primary_contact.last_name}`,
			is_custom: !lead.activation_admin_email
		}
	}

	function handleAdminSelection(event: CustomEvent<AdminEmailSelection>) {
		selected_admin = event.detail
		showEmailCustomization = true
	}

	function resetToDefault() {
		selected_admin = {
			email: lead.primary_contact.email,
			name: `${lead.primary_contact.first_name} ${lead.primary_contact.last_name}`,
			is_custom: false
		}
		showEmailCustomization = false
	}

	// Priority badge styling
	function getPriorityBadgeClass(priority: string) {
		switch (priority) {
			case 'urgent': return 'bg-red-100 text-red-700 border-red-200 dark:bg-red-900 dark:text-red-300'
			case 'high': return 'bg-orange-100 text-orange-700 border-orange-200 dark:bg-orange-900 dark:text-orange-300'
			case 'normal': return 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-300'
			case 'low': return 'bg-blue-100 text-blue-700 border-blue-200 dark:bg-blue-900 dark:text-blue-300'
			default: return 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-300'
		}
	}
</script>

<svelte:head>
  <title>Review Application: {lead.company_name} | Console</title>
</svelte:head>

<div class="space-y-6">
	<div class="flex items-center justify-between">
		<a href="/companies/pending" class="text-sm text-purple-600 hover:text-purple-700 hover:underline">
			&larr; Back to Pending Applications
		</a>
		
		<!-- Domain Detection & Priority Info -->
		<div class="flex items-center gap-3">
			{#if lead.domain_detection_result && lead.domain_extracted}
				<DomainDetectionBadge 
					detection_result={lead.domain_detection_result}
					domain={lead.domain_extracted}
					size="md"
				/>
			{/if}
			
			{#if lead.priority_level !== 'normal'}
				<span class="px-3 py-1 text-sm font-medium border rounded-full {getPriorityBadgeClass(lead.priority_level)}">
					{lead.priority_level.toUpperCase()} PRIORITY
				</span>
			{/if}
		</div>
	</div>

	<div>
		<h1 class="text-3xl font-bold text-gray-900 dark:text-white">Review Application</h1>
		<p class="mt-2 text-gray-600 dark:text-gray-400">
			{lead.domain_detection_result?.type === 'EXISTING_CUSTOMER' 
				? 'New user registration for existing customer domain' 
				: 'New company registration requiring approval'}
		</p>
	</div>

	{#if form?.error}
		<div class="p-4 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
			<div class="flex items-center gap-2">
				<AlertTriangle class="w-5 h-5 text-red-600" />
				<strong class="text-red-800 dark:text-red-200">Error:</strong>
			</div>
			<p class="text-red-700 dark:text-red-300 mt-1">{form.error}</p>
		</div>
	{/if}

	<!-- Special Alert for Existing Customer Domain -->
	{#if lead.domain_detection_result?.type === 'EXISTING_CUSTOMER'}
		<div class="p-4 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
			<div class="flex items-center gap-2 mb-2">
				<Building2 class="w-5 h-5 text-blue-600" />
				<h3 class="font-medium text-blue-900 dark:text-blue-100">Existing Customer Domain Detected</h3>
			</div>
			<p class="text-blue-800 dark:text-blue-200 text-sm">
				This registration is from <strong>{lead.domain_extracted}</strong>, which matches 
				<strong>{lead.domain_detection_result.matched_company_name}</strong>. 
				The company admin has been automatically notified and can approve this registration independently.
			</p>
			{#if lead.company_admin_notified_at}
				<p class="text-blue-700 dark:text-blue-300 text-xs mt-1">
					Company admin notified on {new Date(lead.company_admin_notified_at).toLocaleString()}
				</p>
			{/if}
		</div>
	{/if}

	<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
		<!-- Main Content -->
		<div class="lg:col-span-2 space-y-6">
			<!-- Company Details -->
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
				<h2 class="text-lg font-semibold mb-4 flex items-center gap-2">
					<Building2 class="w-5 h-5"/> Company Information
				</h2>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Company Name</label>
						<p class="text-sm text-gray-900 dark:text-white">{lead.company_name}</p>
					</div>
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Legal Entity Type</label>
						<p class="text-sm text-gray-900 dark:text-white">{lead.company_details.legal_entity_type}</p>
					</div>
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Tax ID</label>
						<p class="text-sm text-gray-900 dark:text-white">{lead.company_details.tax_id}</p>
					</div>
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Business Type</label>
						<p class="text-sm text-gray-900 dark:text-white">{lead.company_details.business_type}</p>
					</div>
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Annual Volume</label>
						<p class="text-sm text-gray-900 dark:text-white">{lead.company_details.estimated_annual_volume}</p>
					</div>
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Services</label>
						<p class="text-sm text-gray-900 dark:text-white">
							{lead.company_details.recruitment_enabled ? 'Recruitment' : ''}
							{lead.company_details.recruitment_enabled && lead.company_details.bench_sales_enabled ? ', ' : ''}
							{lead.company_details.bench_sales_enabled ? 'Bench Sales' : ''}
						</p>
					</div>
					{#if lead.domain_extracted}
						<div class="md:col-span-2">
							<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Email Domain</label>
							<p class="text-sm text-gray-900 dark:text-white font-mono">{lead.domain_extracted}</p>
						</div>
					{/if}
				</div>
			</div>

			<!-- Primary Contact -->
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
				<h2 class="text-lg font-semibold mb-4 flex items-center gap-2">
					<User class="w-5 h-5"/> Primary Contact
				</h2>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</label>
						<p class="text-sm text-gray-900 dark:text-white">{lead.primary_contact.first_name} {lead.primary_contact.last_name}</p>
					</div>
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Title</label>
						<p class="text-sm text-gray-900 dark:text-white">{lead.primary_contact.title}</p>
					</div>
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</label>
						<p class="text-sm text-gray-900 dark:text-white">{lead.primary_contact.email}</p>
					</div>
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</label>
						<p class="text-sm text-gray-900 dark:text-white">{lead.primary_contact.phone}</p>
					</div>
				</div>
			</div>

			<!-- Business Address -->
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
				<h2 class="text-lg font-semibold mb-4 flex items-center gap-2">
					<Globe class="w-5 h-5"/> Business Address
				</h2>
				<div class="space-y-2">
					<p class="text-sm text-gray-900 dark:text-white">{lead.company_details.business_address.street}</p>
					<p class="text-sm text-gray-900 dark:text-white">
						{lead.company_details.business_address.city}, {lead.company_details.business_address.state} {lead.company_details.business_address.zip_code}
					</p>
					<p class="text-sm text-gray-500 dark:text-gray-400">Timezone: {lead.company_details.time_zone}</p>
				</div>
			</div>
		</div>

		<!-- Action Panel -->
		<div class="space-y-6">
			<!-- Application Status -->
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
				<h2 class="text-lg font-semibold mb-4">Application Status</h2>
				<div class="space-y-3">
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</label>
						<p class="text-sm font-semibold capitalize {lead.status === 'pending_review' ? 'text-yellow-600' : lead.status === 'approved' ? 'text-green-600' : 'text-red-600'}">
							{lead.status.replace('_', ' ')}
						</p>
					</div>
					<div>
						<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Submitted</label>
						<p class="text-sm text-gray-900 dark:text-white">{new Date(lead.created_at).toLocaleDateString()}</p>
					</div>
					{#if lead.domain_detection_result}
						<div>
							<label class="text-sm font-medium text-gray-500 dark:text-gray-400">Detection Confidence</label>
							<p class="text-sm text-gray-900 dark:text-white">
								{Math.round(lead.domain_detection_result.confidence_score * 100)}%
							</p>
						</div>
					{/if}
				</div>
			</div>

			<!-- Actions -->
			<div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
				<h2 class="text-lg font-semibold mb-4">Actions</h2>
				{#if lead.status === 'pending_review'}
					<div class="space-y-4">
						<!-- Admin Email Selection -->
						<div class="p-4 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
							<div class="flex items-center justify-between mb-3">
								<h3 class="text-sm font-medium text-purple-900 dark:text-purple-100">
									Activation Email Settings
								</h3>
								{#if !showEmailCustomization}
									<button 
										type="button"
										on:click={() => showEmailCustomization = true}
										class="text-sm text-purple-600 hover:text-purple-700 flex items-center gap-1"
									>
										<Edit class="w-4 h-4" />
										Customize
									</button>
								{/if}
							</div>
							
							{#if showEmailCustomization}
								<AdminEmailSelector 
									company_id={lead.domain_detection_result?.matched_company_id}
									default_email={lead.primary_contact.email}
									default_name="{lead.primary_contact.first_name} {lead.primary_contact.last_name}"
									existing_admins={existing_admins}
									on:selection={handleAdminSelection}
								/>
								
								<div class="mt-3 flex gap-2">
									<button 
										type="button"
										on:click={resetToDefault}
										class="text-sm text-purple-600 hover:text-purple-700 underline"
									>
										Use Default Contact
									</button>
								</div>
							{:else}
								<div class="text-sm text-purple-800 dark:text-purple-200">
									<p><strong>Activation Email:</strong> {selected_admin?.email || lead.primary_contact.email}</p>
									<p><strong>Contact Name:</strong> {selected_admin?.name || `${lead.primary_contact.first_name} ${lead.primary_contact.last_name}`}</p>
								</div>
							{/if}
						</div>

						<!-- Approve Form -->
						<form method="POST" use:enhance={() => { 
							loading = true; 
							return async ({ update }) => { 
								await update(); 
								loading = false; 
							}
						}}>
							{#if selected_admin && showEmailCustomization}
								<input type="hidden" name="activation_email" value={selected_admin.email} />
								<input type="hidden" name="activation_contact_name" value={selected_admin.name} />
								<input type="hidden" name="activation_is_custom" value={selected_admin.is_custom.toString()} />
							{/if}
							
							<button 
								formaction="?/approve" 
								disabled={loading} 
								class="w-full flex justify-center items-center gap-2 px-4 py-2 text-sm font-medium 
									   bg-green-600 text-white hover:bg-green-700 disabled:bg-gray-400 
									   disabled:cursor-not-allowed rounded-md transition-colors"
							>
								<CheckCircle class="w-4 h-4"/> 
								{loading ? 'Approving...' : 'Approve & Send Activation'}
							</button>
							
							{#if selected_admin && showEmailCustomization}
								<p class="text-xs text-gray-600 dark:text-gray-400 mt-2 text-center">
									Activation will be sent to: <strong>{selected_admin.email}</strong>
								</p>
							{/if}
						</form>

						<!-- Reject Form -->
						<form method="POST" use:enhance={() => { 
							loading = true; 
							return async ({ update }) => { 
								await update(); 
								loading = false; 
							}
						}}>
							<div class="space-y-3">
								<label for="rejection_reason" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
									Rejection Reason *
								</label>
								<textarea 
									id="rejection_reason" 
									name="rejection_reason" 
									rows="3"
									class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm 
										   bg-white dark:bg-gray-700 text-gray-900 dark:text-white
										   focus:outline-none focus:ring-2 focus:ring-red-500"
									placeholder="Please provide a reason for rejection..."
									required
								></textarea>
								<button 
									formaction="?/reject" 
									disabled={loading} 
									class="w-full flex justify-center items-center gap-2 px-4 py-2 text-sm font-medium 
										   bg-red-600 text-white hover:bg-red-700 disabled:bg-gray-400 
										   disabled:cursor-not-allowed rounded-md transition-colors"
								>
									<XCircle class="w-4 h-4"/> 
									{loading ? 'Rejecting...' : 'Reject & Notify'}
								</button>
							</div>
						</form>
					</div>
				{:else}
					<div class="space-y-3">
						<p class="text-sm text-gray-500 dark:text-gray-400">
							This application has been <strong class="capitalize">{lead.status.replace('_', ' ')}</strong>.
						</p>
						
						{#if lead.rejection_reason}
							<div class="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
								<p class="text-sm text-red-800 dark:text-red-200"><strong>Rejection Reason:</strong></p>
								<p class="text-sm text-red-700 dark:text-red-300 mt-1">{lead.rejection_reason}</p>
							</div>
						{/if}
						
						{#if lead.activation_admin_email && lead.status === 'approved'}
							<div class="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
								<p class="text-sm text-green-800 dark:text-green-200"><strong>Activation Sent To:</strong></p>
								<p class="text-sm text-green-700 dark:text-green-300 mt-1">
									{lead.activation_admin_name} ({lead.activation_admin_email})
								</p>
							</div>
						{/if}
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>
