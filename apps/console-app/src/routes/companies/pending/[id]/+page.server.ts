import type { PageServerLoad, Actions } from './$types';
import { error, redirect, fail } from '@sveltejs/kit';
import { createSupabaseAdminClient } from '$lib/server/supabase-admin';
import { consoleBusinessRegistrationEmails } from '$lib/business-registration-emails';
import crypto from 'crypto';
import type { BusinessLeadEnhanced, CompanyAdminNotification } from '@psii/shared-types';

export const load: PageServerLoad = async ({ locals: { supabase }, params }) => {
	try {
		// Fetch the business lead with enhanced fields
		const { data: lead, error: leadError } = await supabase
			.from('business_leads')
			.select(`
				*,
				domain_extracted,
				domain_detection_result,
				activation_admin_email,
				activation_admin_name,
				company_admin_notified_at,
				priority_level
			`)
			.eq('id', params.id)
			.single();

		if (leadError || !lead) {
			throw error(404, 'Application not found.');
		}

		const businessLead = lead as BusinessLeadEnhanced;

		// If this is an existing customer domain, fetch existing admins
		let existing_admins: Array<{
			email: string;
			name: string;
			title?: string;
		}> = [];

		if (businessLead.domain_detection_result?.type === 'EXISTING_CUSTOMER' && 
			businessLead.domain_detection_result.matched_company_id) {
			
			const { data: admins } = await supabase
				.from('users')
				.select('email, profile')
				.eq('company_id', businessLead.domain_detection_result.matched_company_id)
				.in('role', ['admin', 'manager'])
				.eq('is_active', true)
				.limit(10);

			if (admins) {
				existing_admins = admins.map(admin => ({
					email: admin.email,
					name: admin.profile?.name || admin.email.split('@')[0],
					title: admin.profile?.title
				}));
			}
		}

		return { 
			lead: businessLead,
			existing_admins
		};

	} catch (e) {
		console.error('Error loading business lead:', e);
		throw error(500, 'Failed to load application details.');
	}
};

async function notifyCompanyAdmins(adminClient: any, notification: CompanyAdminNotification) {
	// This will be implemented with the enhanced email service
	// For now, we'll log the notification
	console.log('[NOTIFICATION] Company admins notified:', notification);
	
	// TODO: Implement actual company admin notification email
	// This would send an email to existing company admins about the new registration
}

export const actions: Actions = {
	approve: async ({ params, request, locals }) => {
		const adminClient = createSupabaseAdminClient();
		const leadId = params.id;

		// Get form data for custom email settings
		const formData = await request.formData();
		const customEmail = formData.get('activation_email') as string;
		const customContactName = formData.get('activation_contact_name') as string;
		const isCustomSelection = formData.get('activation_is_custom') === 'true';

		try {
			// 1. Fetch lead details with enhanced fields
			const { data: lead, error: fetchError } = await adminClient
				.from('business_leads')
				.select(`
					*,
					domain_extracted,
					domain_detection_result,
					activation_admin_email,
					activation_admin_name,
					company_admin_notified_at,
					priority_level
				`)
				.eq('id', leadId)
				.single();

			if (fetchError || !lead) {
				return fail(404, { error: 'Application not found.' });
			}

			const businessLead = lead as BusinessLeadEnhanced;
			
			if (businessLead.status !== 'pending_review') {
				return fail(400, { error: 'This application has already been processed.' });
			}

			// Determine activation email and contact name
			const activationEmail = customEmail || businessLead.primary_contact.email;
			const activationContactName = customContactName || 
				`${businessLead.primary_contact.first_name} ${businessLead.primary_contact.last_name}`;

			// Validate custom email if provided
			if (customEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customEmail)) {
				return fail(400, { error: 'Invalid email address provided.' });
			}

			// Check if this is for an existing customer domain
			let company_id: string;
			
			if (businessLead.domain_detection_result?.type === 'EXISTING_CUSTOMER' && 
				businessLead.domain_detection_result.matched_company_id) {
				
				// Use existing company
				company_id = businessLead.domain_detection_result.matched_company_id;
				
				// Notify existing company admins if not already notified
				if (!businessLead.company_admin_notified_at) {
					await notifyCompanyAdmins(adminClient, {
						company_id,
						company_name: businessLead.domain_detection_result.matched_company_name || businessLead.company_name,
						admin_emails: [activationEmail],
						registration_id: leadId,
						applicant_name: activationContactName,
						applicant_email: businessLead.primary_contact.email,
						domain: businessLead.domain_extracted || ''
					});

					// Update notification timestamp
					await adminClient
						.from('business_leads')
						.update({ company_admin_notified_at: new Date().toISOString() })
						.eq('id', leadId);
				}

			} else {
				// Create new company record
				const { data: newCompany, error: companyError } = await adminClient
					.from('companies')
					.insert({
						name: businessLead.company_name,
						domain: businessLead.domain_extracted,
						details: businessLead.company_details,
						registration_status: 'approved',
						business_type: businessLead.company_details.business_type,
						legal_entity_type: businessLead.company_details.legal_entity_type,
						tax_id: businessLead.company_details.tax_id,
						business_address: businessLead.company_details.business_address,
						primary_contact: businessLead.primary_contact,
						estimated_annual_volume: businessLead.company_details.estimated_annual_volume,
						time_zone: businessLead.company_details.time_zone,
						working_hours: businessLead.company_details.working_hours,
						recruitment_enabled: businessLead.company_details.recruitment_enabled,
						bench_sales_enabled: businessLead.company_details.bench_sales_enabled
					})
					.select()
					.single();

				if (companyError) {
					console.error('Failed to create company:', companyError);
					throw companyError;
				}

				company_id = newCompany.id;
			}

			// Generate secure activation token
			const activationToken = crypto.randomBytes(32).toString('hex');
			const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days from now

			// Create activation token record
			const { error: tokenError } = await adminClient
				.from('activation_tokens')
				.insert({
					company_id: company_id,
					token: activationToken,
					contact_email: activationEmail,
					contact_name: activationContactName,
					expires_at: expiresAt.toISOString(),
					process_permissions: ['recruitment', 'bench_sales'] // Default permissions
				});

			if (tokenError) {
				console.error('Failed to create activation token:', tokenError);
				return fail(500, { error: 'Failed to create activation token' });
			}

			// Generate activation link
			const activationLink = `${process.env.PUBLIC_SITE_URL || 'http://localhost:5173'}/auth/activate?token=${activationToken}&company_id=${company_id}`;

			// Send approval email
			await consoleBusinessRegistrationEmails.sendRegistrationApprovedEmail({
				company_name: businessLead.company_name,
				contact_name: activationContactName,
				contact_email: activationEmail,
				submission_date: new Date(businessLead.created_at).toLocaleDateString(),
				registration_id: businessLead.id,
				activation_link: activationLink,
				company_id: company_id,
				// Include original contact info for reference if using custom email
				original_contact: isCustomSelection ? {
					name: `${businessLead.primary_contact.first_name} ${businessLead.primary_contact.last_name}`,
					email: businessLead.primary_contact.email
				} : undefined
			});

			// Update lead status with admin selection details
			const { error: updateError } = await adminClient
				.from('business_leads')
				.update({ 
					status: 'approved',
					activation_admin_email: activationEmail,
					activation_admin_name: activationContactName
				})
				.eq('id', leadId);

			if (updateError) {
				console.error('Failed to update lead status:', updateError);
			}

			// Log the successful approval
			console.log(`[APPROVAL] Business registration approved: ${businessLead.company_name} (${leadId})`);
			console.log(`[ACTIVATION] Email sent to: ${activationEmail} (${activationContactName})`);

		} catch (e: any) {
			console.error('Error during approval process:', e);
			return fail(500, { error: `Approval failed: ${e.message}` });
		}

		throw redirect(303, '/companies/pending?status=approved');
	},

	reject: async ({ params, request, locals }) => {
		const adminClient = createSupabaseAdminClient();
		const leadId = params.id;
		
		// Get rejection reason from form data
		const formData = await request.formData();
		const rejectionReason = formData.get('rejection_reason') as string || 'No reason provided';

		try {
			// 1. Fetch lead details
			const { data: lead, error: fetchError } = await adminClient
				.from('business_leads')
				.select('*')
				.eq('id', leadId)
				.single();

			if (fetchError || !lead) {
				return fail(404, { error: 'Application not found.' });
			}

			const businessLead = lead as BusinessLeadEnhanced;

			if (businessLead.status !== 'pending_review') {
				return fail(400, { error: 'This application has already been processed.' });
			}

			// Send rejection email
			await consoleBusinessRegistrationEmails.sendRegistrationRejectedEmail({
				company_name: businessLead.company_name,
				contact_name: `${businessLead.primary_contact.first_name} ${businessLead.primary_contact.last_name}`,
				contact_email: businessLead.primary_contact.email,
				submission_date: new Date(businessLead.created_at).toLocaleDateString(),
				registration_id: businessLead.id,
				rejection_reason: rejectionReason,
				support_email: '<EMAIL>'
			});

			// Update lead status
			const { error: updateError } = await adminClient
				.from('business_leads')
				.update({ 
					status: 'rejected',
					rejection_reason: rejectionReason
				})
				.eq('id', leadId);

			if (updateError) {
				throw updateError;
			}

			// Log the rejection
			console.log(`[REJECTION] Business registration rejected: ${businessLead.company_name} (${leadId})`);
			console.log(`[REJECTION] Reason: ${rejectionReason}`);

		} catch (e: any) {
			console.error('Error during rejection process:', e);
			return fail(500, { error: `Rejection failed: ${e.message}` });
		}

		throw redirect(303, '/companies/pending?status=rejected');
	}
};
