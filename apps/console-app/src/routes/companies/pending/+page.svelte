<script lang="ts">
	import type { PageData } from './$types'
	import { FileText, Building2, Filter, Download, RefreshCw } from 'lucide-svelte'
	import { onMount } from 'svelte'
	import { invalidate } from '$app/navigation'
	import DomainDetectionBadge from '$lib/components/registration/DomainDetectionBadge.svelte'
	import type { BusinessLeadEnhanced } from '@psii/shared-types'

	export let data: PageData

	$: leads = (data.leads || []) as BusinessLeadEnhanced[]
	$: analytics = data.analytics || {
		total_registrations: 0,
		pending_count: 0,
		approved_count: 0,
		rejected_count: 0,
		new_company_count: 0,
		existing_domain_count: 0,
		avg_approval_time_hours: 0
	}

	// Filtering and sorting
	let filter_status: 'all' | 'pending_review' | 'approved' | 'rejected' = 'all'
	let filter_domain_type: 'all' | 'NEW_COMPANY' | 'EXISTING_CUSTOMER' = 'all'
	let filter_priority: 'all' | 'low' | 'normal' | 'high' | 'urgent' = 'all'
	let sort_by: 'created_at' | 'company_name' | 'priority_level' = 'created_at'
	let sort_order: 'asc' | 'desc' = 'desc'

	// Filtered and sorted leads
	$: filteredLeads = leads
		.filter(lead => {
			if (filter_status !== 'all' && lead.status !== filter_status) return false
			if (filter_domain_type !== 'all' && lead.domain_detection_result?.type !== filter_domain_type) return false
			if (filter_priority !== 'all' && lead.priority_level !== filter_priority) return false
			return true
		})
		.sort((a, b) => {
			let comparison = 0
			
			if (sort_by === 'created_at') {
				comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
			} else if (sort_by === 'company_name') {
				comparison = a.company_name.localeCompare(b.company_name)
			} else if (sort_by === 'priority_level') {
				const priorityOrder = { 'urgent': 4, 'high': 3, 'normal': 2, 'low': 1 }
				comparison = (priorityOrder[a.priority_level] || 2) - (priorityOrder[b.priority_level] || 2)
			}
			
			return sort_order === 'desc' ? -comparison : comparison
		})

	// Auto-refresh functionality
	let auto_refresh = false
	let refresh_interval: NodeJS.Timeout | null = null

	function toggleAutoRefresh() {
		auto_refresh = !auto_refresh
		
		if (auto_refresh) {
			refresh_interval = setInterval(() => {
				invalidate('app:pending-registrations')
			}, 30000) // Refresh every 30 seconds
		} else if (refresh_interval) {
			clearInterval(refresh_interval)
			refresh_interval = null
		}
	}

	function manualRefresh() {
		invalidate('app:pending-registrations')
	}

	// Priority badge styling
	function getPriorityBadgeClass(priority: string) {
		switch (priority) {
			case 'urgent': return 'bg-red-100 text-red-700 border-red-200 dark:bg-red-900 dark:text-red-300'
			case 'high': return 'bg-orange-100 text-orange-700 border-orange-200 dark:bg-orange-900 dark:text-orange-300'
			case 'normal': return 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-300'
			case 'low': return 'bg-blue-100 text-blue-700 border-blue-200 dark:bg-blue-900 dark:text-blue-300'
			default: return 'bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-300'
		}
	}

	onMount(() => {
		return () => {
			if (refresh_interval) {
				clearInterval(refresh_interval)
			}
		}
	})
</script>

<svelte:head>
  <title>Pending Applications | Console</title>
</svelte:head>

<div class="space-y-6">
  <!-- Header with Analytics -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Pending Applications</h1>
      <p class="text-gray-600 dark:text-gray-400">Review and approve new business registrations.</p>
    </div>
    
    <div class="flex items-center gap-4">
      <!-- Auto Refresh Toggle -->
      <button
        on:click={toggleAutoRefresh}
        class="flex items-center gap-2 px-3 py-2 text-sm border rounded-lg transition-colors
               {auto_refresh 
                 ? 'bg-green-50 border-green-200 text-green-700 dark:bg-green-900 dark:border-green-700 dark:text-green-300' 
                 : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300'}"
      >
        <RefreshCw class="w-4 h-4 {auto_refresh ? 'animate-spin' : ''}" />
        Auto Refresh
      </button>
      
      <!-- Manual Refresh -->
      <button
        on:click={manualRefresh}
        class="flex items-center gap-2 px-3 py-2 text-sm border border-gray-200 rounded-lg 
               bg-white hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700"
      >
        <RefreshCw class="w-4 h-4" />
        Refresh
      </button>
    </div>
  </div>

  <!-- Analytics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400">Total Registrations</p>
          <p class="text-2xl font-semibold text-gray-900 dark:text-white">{analytics.total_registrations}</p>
        </div>
        <FileText class="w-8 h-8 text-gray-400" />
      </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400">Pending Review</p>
          <p class="text-2xl font-semibold text-yellow-600">{analytics.pending_count}</p>
        </div>
        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
          <FileText class="w-5 h-5 text-yellow-600" />
        </div>
      </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400">New Companies</p>
          <p class="text-2xl font-semibold text-green-600">{analytics.new_company_count}</p>
        </div>
        <Building2 class="w-8 h-8 text-green-400" />
      </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400">Existing Domains</p>
          <p class="text-2xl font-semibold text-blue-600">{analytics.existing_domain_count}</p>
        </div>
        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
          <Building2 class="w-5 h-5 text-blue-600" />
        </div>
      </div>
    </div>
  </div>

  <!-- Filters and Controls -->
  <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
    <div class="flex flex-wrap items-center gap-4">
      <div class="flex items-center gap-2">
        <Filter class="w-5 h-5 text-gray-500" />
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Filters:</span>
      </div>
      
      <!-- Status Filter -->
      <select
        bind:value={filter_status}
        class="px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md 
               bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
      >
        <option value="all">All Status</option>
        <option value="pending_review">Pending Review</option>
        <option value="approved">Approved</option>
        <option value="rejected">Rejected</option>
      </select>
      
      <!-- Domain Type Filter -->
      <select
        bind:value={filter_domain_type}
        class="px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md 
               bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
      >
        <option value="all">All Types</option>
        <option value="NEW_COMPANY">New Companies</option>
        <option value="EXISTING_CUSTOMER">Existing Customers</option>
      </select>
      
      <!-- Priority Filter -->
      <select
        bind:value={filter_priority}
        class="px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md 
               bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
      >
        <option value="all">All Priorities</option>
        <option value="urgent">Urgent</option>
        <option value="high">High</option>
        <option value="normal">Normal</option>
        <option value="low">Low</option>
      </select>
      
      <!-- Sort Controls -->
      <div class="flex items-center gap-2 ml-auto">
        <span class="text-sm text-gray-500 dark:text-gray-400">Sort by:</span>
        <select
          bind:value={sort_by}
          class="px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md 
                 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          <option value="created_at">Date</option>
          <option value="company_name">Company</option>
          <option value="priority_level">Priority</option>
        </select>
        
        <button
          on:click={() => sort_order = sort_order === 'asc' ? 'desc' : 'asc'}
          class="px-2 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md 
                 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          {sort_order === 'asc' ? '↑' : '↓'}
        </button>
      </div>
    </div>
  </div>

  <!-- Applications List -->
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
		<div class="p-4 border-b border-gray-200 dark:border-gray-700">
			<h2 class="text-lg font-semibold flex items-center gap-2">
				<FileText class="w-5 h-5" />
				Applications ({filteredLeads.length})
			</h2>
		</div>
		
		<div class="p-4">
			{#if filteredLeads.length === 0}
				<div class="text-center py-12">
					<Building2 class="w-12 h-12 text-gray-400 mx-auto mb-4" />
					<h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
						{leads.length === 0 ? 'No pending applications' : 'No applications match your filters'}
					</h3>
					<p class="text-gray-500 dark:text-gray-400">
						{leads.length === 0 
							? 'New applications from the customer portal will appear here.' 
							: 'Try adjusting your filter criteria.'}
					</p>
				</div>
			{:else}
				<div class="space-y-3">
					{#each filteredLeads as lead}
						<div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg 
								hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
							<div class="flex items-center space-x-4 flex-1">
								<!-- Company Icon -->
								<div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
									<Building2 class="w-5 h-5 text-purple-600 dark:text-purple-300" />
								</div>
								
								<!-- Company Information -->
								<div class="flex-1 min-w-0">
									<div class="flex items-center gap-3 mb-1">
										<h3 class="font-semibold text-gray-900 dark:text-white truncate">
											{lead.company_name}
										</h3>
										
										<!-- Domain Detection Badge -->
										{#if lead.domain_detection_result && lead.domain_extracted}
											<DomainDetectionBadge 
												detection_result={lead.domain_detection_result}
												domain={lead.domain_extracted}
												size="sm"
											/>
										{/if}
										
										<!-- Priority Badge -->
										{#if lead.priority_level !== 'normal'}
											<span class="px-2 py-1 text-xs font-medium border rounded-full {getPriorityBadgeClass(lead.priority_level)}">
												{lead.priority_level.toUpperCase()}
											</span>
										{/if}
									</div>
									
									<p class="text-sm text-gray-600 dark:text-gray-400">
										{lead.primary_contact.first_name} {lead.primary_contact.last_name} 
										<span class="text-gray-400">({lead.primary_contact.email})</span>
									</p>
									
									<div class="flex items-center gap-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
										<span>Submitted: {new Date(lead.created_at).toLocaleDateString()}</span>
										{#if lead.domain_extracted}
											<span>Domain: {lead.domain_extracted}</span>
										{/if}
										{#if lead.company_admin_notified_at}
											<span class="text-blue-600">Company admin notified</span>
										{/if}
									</div>
								</div>
							</div>
							
							<!-- Actions -->
							<a href={`/companies/pending/${lead.id}`} 
							   class="px-4 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600 
								   bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 
								   text-gray-700 dark:text-gray-300 rounded-md transition-colors">
								Review
							</a>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	</div>
</div>
