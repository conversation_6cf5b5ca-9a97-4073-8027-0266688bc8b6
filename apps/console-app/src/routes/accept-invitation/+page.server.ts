import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'

// This route is deprecated - redirect to the correct activation route
export const load: PageServerLoad = async ({ url }) => {
  const token = url.searchParams.get('token')
  
  if (token) {
    throw redirect(301, `/auth/activate-console?token=${token}`)
  } else {
    throw redirect(301, '/login?error=missing_token')
  }
}
