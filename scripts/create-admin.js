#!/usr/bin/env node

/**
 * Direct Admin User Creation Script
 * Creates admin user directly in Supabase Auth and console_users table
 */

import { createClient } from '@supabase/supabase-js'
import { config } from 'dotenv'
import { join } from 'path'
import { randomUUID } from 'crypto'

// Load environment variables
config({ path: join(process.cwd(), '.env.local') })

const SUPABASE_URL = process.env.PUBLIC_SUPABASE_URL
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY
const ADMIN_EMAIL = '<EMAIL>'
const ADMIN_PASSWORD = 'admin123'

async function createAdminUser() {
  console.log('🔧 Creating Admin User Directly')
  console.log('================================\n')

  if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
    console.error('❌ Missing Supabase configuration')
    process.exit(1)
  }

  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })

  try {
    // Step 1: Check if auth user already exists
    console.log('1️⃣ Checking for existing Auth user...')
    const { data: existingAuthUsers } = await supabase.auth.admin.listUsers()
    const existingAuthUser = existingAuthUsers?.users?.find(u => u.email === ADMIN_EMAIL)
    
    let authUserId

    if (existingAuthUser) {
      console.log('   ✅ Auth user already exists:', existingAuthUser.id)
      authUserId = existingAuthUser.id
    } else {
      // Create auth user
      console.log('   🔨 Creating Auth user...')
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
        email_confirm: true,
        user_metadata: {
          created_via: 'admin_script',
          role: 'super_admin'
        }
      })

      if (authError || !authData.user) {
        console.error('   ❌ Failed to create Auth user:', authError)
        process.exit(1)
      }

      authUserId = authData.user.id
      console.log('   ✅ Auth user created:', authUserId)
    }

    // Step 2: Check if console user exists
    console.log('\n2️⃣ Checking for existing Console user...')
    const { data: existingConsoleUser } = await supabase
      .from('console_users')
      .select('*')
      .eq('email', ADMIN_EMAIL)
      .single()

    if (existingConsoleUser) {
      console.log('   ✅ Console user already exists:', existingConsoleUser.id)
    } else {
      // Create console user
      console.log('   🔨 Creating Console user...')
      const { error: consoleError } = await supabase
        .from('console_users')
        .insert({
          id: authUserId,
          email: ADMIN_EMAIL,
          role: 'super_admin',
          company_ids: ['procureserve-internal'],
          is_active: true,
          mfa_enabled: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (consoleError) {
        console.error('   ❌ Failed to create Console user:', consoleError)
        process.exit(1)
      }
      console.log('   ✅ Console user created')
    }

    // Step 3: Create/update permissions
    console.log('\n3️⃣ Setting up permissions...')
    
    // Delete existing permissions first
    await supabase
      .from('console_user_permissions')
      .delete()
      .eq('user_id', authUserId)

    const permissions = [
      {
        user_id: authUserId,
        resource: 'console_users',
        actions: ['create', 'read', 'update', 'delete', 'invite', 'manage'],
        company_id: null
      },
      {
        user_id: authUserId,
        resource: 'companies',
        actions: ['create', 'read', 'update', 'delete', 'manage'],
        company_id: null
      },
      {
        user_id: authUserId,
        resource: 'enums',
        actions: ['create', 'read', 'update', 'delete', 'manage'],
        company_id: null
      },
      {
        user_id: authUserId,
        resource: 'settings',
        actions: ['read', 'update', 'manage'],
        company_id: null
      },
      {
        user_id: authUserId,
        resource: 'audit_logs',
        actions: ['read'],
        company_id: null
      },
      {
        user_id: authUserId,
        resource: 'analytics',
        actions: ['read'],
        company_id: null
      }
    ]

    const { error: permissionsError } = await supabase
      .from('console_user_permissions')
      .insert(permissions)

    if (permissionsError) {
      console.error('   ❌ Failed to create permissions:', permissionsError)
    } else {
      console.log('   ✅ Permissions created')
    }

    // Step 4: Log security event
    console.log('\n4️⃣ Logging security event...')
    const { error: securityLogError } = await supabase
      .from('console_security_events')
      .insert({
        id: randomUUID(),
        event_type: 'initial_setup_completed',
        user_id: authUserId,
        user_email: ADMIN_EMAIL,
        user_role: 'super_admin',
        success: true,
        metadata: {
          created_via: 'admin_script',
          timestamp: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      })

    if (securityLogError) {
      console.error('   ⚠️ Failed to log security event:', securityLogError)
    } else {
      console.log('   ✅ Security event logged')
    }

    // Success summary
    console.log('\n🎉 Admin User Creation Complete!')
    console.log('================================')
    console.log(`📧 Email: ${ADMIN_EMAIL}`)
    console.log(`🔑 Password: ${ADMIN_PASSWORD}`)
    console.log(`👤 User ID: ${authUserId}`)
    console.log(`🌐 Login URL: http://localhost:3008/login`)
    console.log('\n⚠️ Remember to:')
    console.log('   • Set ENABLE_INITIAL_SETUP=false in .env.local')
    console.log('   • Change the password after first login')
    console.log('   • Test the login functionality')

  } catch (error) {
    console.error('❌ Script failed:', error)
    process.exit(1)
  }
}

createAdminUser().catch(console.error)