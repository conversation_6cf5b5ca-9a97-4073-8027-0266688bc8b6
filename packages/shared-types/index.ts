// Enhanced Business Registration Types with Domain Detection
export interface DomainDetectionResult {
  type: 'NEW_COMPANY' | 'EXISTING_CUSTOMER'
  matched_company_id?: string
  matched_company_name?: string  
  confidence_score: number
  related_domains?: string[]
  detection_timestamp: string
}

export interface BusinessLeadEnhanced {
  id: string
  company_name: string
  primary_contact: {
    first_name: string
    last_name: string
    email: string
    phone: string
    title: string
  }
  company_details: {
    legal_entity_type: string
    tax_id: string
    business_type: string
    business_address: {
      street: string
      city: string
      state: string
      zip_code: string
    }
    estimated_annual_volume: string
    time_zone: string
    working_hours: {
      start: string
      end: string
    }
    recruitment_enabled: boolean
    bench_sales_enabled: boolean
  }
  status: 'pending_review' | 'approved' | 'rejected'
  rejection_reason?: string
  created_at: string
  updated_at: string
  
  // Enhanced fields for domain detection
  domain_extracted?: string
  domain_detection_result?: DomainDetectionResult
  activation_admin_email?: string
  activation_admin_name?: string
  company_admin_notified_at?: string
  priority_level: 'low' | 'normal' | 'high' | 'urgent'
}

export interface AdminEmailSelection {
  email: string
  name: string
  title?: string
  is_custom: boolean // true if typed manually, false if selected from existing
}

export interface CompanyAdminNotification {
  company_id: string
  company_name: string
  admin_emails: string[]
  registration_id: string
  applicant_name: string
  applicant_email: string
  domain: string
}

export interface RegistrationAnalytics {
  total_registrations: number
  pending_count: number
  approved_count: number
  rejected_count: number
  new_company_count: number
  existing_domain_count: number
  avg_approval_time_hours: number
  domains_breakdown: {
    domain: string
    count: number
    last_registration: string
  }[]
}

// UI Component Props
export interface DomainDetectionBadgeProps {
  detection_result: DomainDetectionResult
  domain: string
  size?: 'sm' | 'md' | 'lg'
}

export interface AdminEmailSelectorProps {
  company_id?: string
  default_email?: string
  default_name?: string
  on_selection: (selection: AdminEmailSelection) => void
  existing_admins?: Array<{
    email: string
    name: string
    title?: string
  }>
}
